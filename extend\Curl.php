<?php


use think\Exception;

class Curl
{


    /**
     * @方法描述: 获取T+业务员
     * <AUTHOR>
     * @Date 2022/9/27 16:04
     * @param $param
     * $param[name]    否    string    业务员名称
     * $param[account_no]    否    string    账套 默认002
     * @return mixed
     * @throws Exception
     */
    static public function getTPlusClerk($param = [])
    {
        $method = "/pushtplus/v3/clerk";
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : '';
        $url    = env('ITEM.PUSH_T_PLUS_URL') . $method . $query;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('获取T+业务员失败: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取T+仓库
     * <AUTHOR>
     * @Date 2022/9/27 16:04
     * @param $param
     * $param[name]    否    string    仓库名称
     * $param[code]    否    string    仓库编码
     * $param[account_no]    否    string    账套 默认002
     * @return mixed
     * @throws Exception
     */
    static public function getTPlusWarehouse($param = [])
    {
        $method = "/pushtplus/v3/warehouse";
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : '';
        $url    = env('ITEM.PUSH_T_PLUS_URL') . $method . $query;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('获取T+业务员失败: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取管理员信息
     * <AUTHOR>
     * @Date 2022/9/27 14:20
     * @param $param
     * $param[admin_id]    是    string    管理员ID(多个id用英文半角逗号分隔)
     * $param[field]    否    string    指定字段不传默认(id,realname,title,userid)
     * @return mixed
     * @throws Exception
     */
    static public function adminInfo($param)
    {
        //Array
        //(
        //    [94] => Array
        //        (
        //            [id] => 94
        //            [encrypt_id] => ft9qPmrGZGo=
        //            [telephone] => 17772336502
        //            [realname] => 陈朝桃
        //            [avatar] => https://wework.qpic.cn/wwpic/81135_Q6ON50g9Sj2NDJc_1661782550/0
        //            [title] => 后端工程师
        //            [dept_id] => ["5"]
        //            [department] => 后端组
        //            [userid] => ChenChaoTao
        //            [status] => 1
        //            [is_delete] => 1
        //            [label] => 3
        //            [token] => eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6OTQsInRlbGVwaG9uZSI6IjE3NzcyMzM2NTAyIiwidGltZSI6MTcwNjQyMTE2OH0.U2rs4VGjzMGrnNyHtRNo0wDXEwYVfiVupdMx2H6oOwg
        //            [last_login_time] => 1708996427
        //            [operator] => 94
        //            [created_time] => 1659321545
        //            [update_time] => 1706421168
        //        )
        //
        //)
        $method = "/authority/v3/admin/info";
        $url    = env('ITEM.AUTHORITY_URL') . $method . '?' . self::array_to_url_prarm($param);

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取管理员列表
     * <AUTHOR>
     * @Date 2022/9/27 14:20
     * @param $param
     * $param[role_id]    否    int    角色ID
     * $param[telephone]    否    int    手机号
     * $param[realname]    否    string    真实姓名
     * $param[limit]    是    int    返回条数
     * $param[page]    是    int    当前页
     * @return mixed
     * @throws Exception
     */
    static public function adminList($param)
    {
        $method = "/authority/v3/admin/list";
        $url    = env('ITEM.AUTHORITY_URL') . $method . '?' . self::array_to_url_prarm($param);

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['data'];
    }

    /**
     * @方法描述: 获取指定管理员列表
     * <AUTHOR>
     * @Date 2022/9/16 16:59
     * @param $type int 列表类型：2采购组，3文案组，4客服组，20用户查询, 35订单查询机器人
     * @return mixed
     * @throws Exception
     */
    static public function getSpecifyList($type)
    {
        $method = "/authority/v3/admin/specifyList";
        $url    = env('ITEM.AUTHORITY_URL') . $method . '?type=' . $type;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['data']['list'];
    }

    /**
     * @方法描述: 取出不为空结果
     * <AUTHOR>
     * @Date 2022/9/27 14:27
     * @param $method
     * @param $args
     * @return mixed
     * @throws \think\Exception
     */
    static public function notEmptyResult($method, $args)
    {
        $result = self::$method(...$args);
        if (empty($result)) {
            throw new \think\Exception(" {$method} 查询结果为空");
        }
        return $result;
    }

    /**
     * @方法描述:数组转 Query url
     * <AUTHOR>
     * @Date 2022/9/27 13:26
     * @param $array
     * @return string
     */
    static public function array_to_url_prarm($array)
    {
        $prarms = [];

        foreach ($array as $key => $val) {
            $prarms[] = $key . '=' . str_replace(' ', '+', $val);
        }

        return implode('&', $prarms);
    }

    /**
     * @方法描述: 获取用户信息
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $uid
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function getUserInfo($uid, $fields = 'uid,telephone,nickname,avatar_image,user_level,type')
    {
        return self::getUserInfoList($uid, $fields)[0];
    }

    /**
     * @方法描述: 获取用户信息列表
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $uid
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function getUserInfoList($uid, $fields = 'uid,telephone,nickname,avatar_image,user_level,type')
    {
        $method = '/user/v3/profile/getUserInfo';
        $url    = env('ITEM.USER_URL') . $method . '?uid=' . $uid . "&field=$fields";

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data']['list'][0])) {
            throw new Exception('未找到用户');
        }

        return $result['data']['list'];
    }

    /**
     * @方法描述: 数据加解密
     * <AUTHOR>
     * @Date 2022/10/11 10:37
     * @param array $param 参数
     * @param string $type 类型：D解密，E加密
     * @param int $i 请求次数
     * @return array|\think\Response
     */
    static public function cryptionDeal($orig_data, $type = 'D', $i = 1)
    {
        return cryptionDeal([
            'orig_data' => $orig_data,
            'uid'       => '8888888888',
            'operator'  => 'tp6-auction'
        ], $type, $i);
    }

    /**
     * @方法描述: 获取企业微信accesstoken
     * <AUTHOR>
     * @Date 2022/10/11 11:35
     * @param array $param
     * $param[corpid]    string    否    -
     * $param[agentid]    string    否    -
     * 不需要传递corpid和agentid代表获取vinehoo中台应用的accesstoken，大多数情况下是获取vinehoo中台应用
     * @return mixed
     * @throws Exception
     */
    static public function getWechatAccessToken($param = [])
    {
        $method = "/wechat/v3/wecom/accesstoken";
        $url    = env('ITEM.WECHART_URL') . $method;
        $query  = self::array_to_url_prarm($param);
        $query  = $query ? '?' . $query : $query;
        $url    .= $query;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('查询管理员列表错误: ' . $result['error_msg']);
        }

        return $result['access_token'];
    }

    public static function downloadGet($url, $file)
    {
        return file_put_contents($file, file_get_contents($url));
    }

    /**
     * @方法描述: 上传临时文件到微信
     * <AUTHOR>
     * @Date 2022/10/11 20:58
     * @param $filePath
     * @param string $type
     * @return bool
     * @throws Exception
     */
    static public function upTempFileToWechat($filePath, $type = 'file')
    {
        $accessToken = self::getWechatAccessToken();
        $url         = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={$accessToken}&type={$type}";
        $info        = new \CURLFile(realpath($filePath));
        $info->setPostFilename('用户ID,手机号导出订单审批-' . date('Y-m-d H:i:s') . '.xlsx');
        $data   = [
            'media' => $info,
        ];
        $header = [];

        $result = self::curl_post($url, $data, $header);
        $data   = @json_decode($result, true);

        if (!$data || $data['errcode'] != 0) {
            throw new Exception('上传文件到微信失败! result: ' . $result);
        }

        return $data;
    }

    /**
     * @方法描述: 发起 POST 请求
     * <AUTHOR>
     * @Date 2022/10/11 20:53
     * @param $url
     * @param null $data
     * @return bool|string
     */
    static public function curl_post($url, $data = null, $header = [])
    {
        //创建一个新cURL资源
        $curl = curl_init();
        //设置URL和相应的选项
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);

        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }

        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header); //设置头信息
        }

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        //执行curl，抓取URL并把它传递给浏览器
        $output = curl_exec($curl);
        //关闭cURL资源，并且释放系统资源
        curl_close($curl);
        return $output;
    }

    /**
     * 发送应用消息
     */
    static public function wecomSend($content, $userid, $msgtype, $agentid = 0)
    {
        $method = '/wechat/v3/wecom/app/send';
        $url    = env('ITEM.WECHART_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode(compact('content', 'userid', 'msgtype', 'agentid')), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            throw new Exception('发送应用消息失败: ' . $result['error_msg']);
        }

        return true;
    }

    /**
     * @方法描述: 获取用户信息列表
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $param ['ips'] array
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function naliIp($param)
    {
        $method = '/services/v3/ip/check';
        $url    = env('ITEM.NALI_IP_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("naliIp 查找IP地址错误 " . json_encode([$url, 'post', $param]));
            throw new Exception('查找IP地址错误');
        }

        $data = [];
        foreach ($result['data'] as $item) {
            foreach ($item as $ip => $addr) {
                $data[$ip] = explode('|', $addr);
            }
        }

        return $data;
    }

    /**
     * @方法描述: 拍卖 添加消息通知
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @param $param ['notice_type'] int 类型1:拍卖，2:通知，3:订单，4:评论
     * @param $param ['title'] string 标题
     * @param $param ['content'] string 内容
     * @param $param ['uid'] string 发送给那个用户
     * @param $param ['notice_data'] 对应的参数 例如：订单的信息 评论的信息。具体内容请看设计图的消息详情
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function message($param, $headers = [])
    {
        if (isset($param['uid'])) {
            $param['uid'] = strval($param['uid']);
        }
        $method = '/message/v3/notice/noticeAdd';
        $url    = env('ITEM.MESSAGE_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3, $headers);
        $result = json_decode($code, true);


        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("message 添加消息通知错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
//            throw new Exception('添加消息通知错误, ' . ($result['error_msg'] ?? ''));
        }

        return true;
    }

    /**
     * @方法描述: 拍卖 APP 批量（单个）用户透传推送
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @文档地址 https://showdoc.wineyun.com//web/#/89/3899
     * @param $param ['transmission'] string json字符串（营销推送配置的模版数据 文档地址：https://showdoc.wineyun.com/web/#/24/2716）
     * @param $param ['title'] string 标题
     * @param $param ['content'] string 内容
     * @param $param ['notice_type'] int 通知类型(101:拍卖系统通知)
     * @param $param ['uids'] int array 推送人的用户id
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function appPushAliasBatch($param, $headers = [])
    {
        $uids   = [];
        $p_uids = $param['uids'] ?? [];
        foreach ($p_uids as $uid) {
            $uids[] = intval($uid);
        }
        $param['uids'] = $uids;

        $method = '/message/v3/push/appPushAliasBatch';
        $url    = env('ITEM.MESSAGE_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3, $headers);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("appPushAliasBatch 用户透传推送错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            //throw new Exception('用户透传推送错误, ' . ($result['error_msg'] ?? ''));
        }

        return true;
    }


    /**
     * @方法描述: 客户端路径配置-查询通过唯一标识
     * <AUTHOR>
     * @Date 2022/8/24 17:39
     * @文档地址 https://showdoc.wineyun.com/web/#/24/2716
     * @param $param ['code'] 必选 string 唯一标识
     * @param $param ['rep_type'] 可选 string  "1"=自定义配置路径
     * @param $param ['goods_id'] 可选 string  rep_type=3 必选. 拍品ID
     * @param string $fields
     * @return mixed
     * @throws Exception
     */
    static public function getClientpathByCode($param)
    {
        $method = '/marketing-conf/v3/clientpath/codefilterlist?' . self::array_to_url_prarm($param);
        $url    = env('ITEM.MARKET_CONF_URL') . $method;

        $code   = httpCurl($url, 'get', [], 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            \think\facade\Log::error("getClientpathByCode 获取客户端路径配置错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception('获取客户端路径配置错误, ' . $result['error_msg'] ?? '');
        }

        if (!empty($param ['rep_type']) && in_array($param ['rep_type'], ["1"])) {
            $rep_urls = [
                '1' => "xxxx", //自定义路径
            ];
            $url      = $rep_urls[$param ['rep_type']] ?? null;
            if ($url) {
                $result['data']['client_path_param'][0]['ios_val']     = $url;
                $result['data']['client_path_param'][0]['android_val'] = $url;
            }
        }

        return $result['data'];
    }

    /**
     * @方法描述:添加秒级任务
     * <AUTHOR>
     * @Date 2022/12/19 22:18
     * @param $param
     * @param $param ['task_id'] String 必传 任务ID，请使用GUID方式生成唯一值
     * @param $param ['task_trigger_time'] TimeStamp 必传 任务触发时间，10位时间戳
     * @param $param ['task_url'] String 必传 任务触发时调用的接口地址，必须带上HTTP或HTTPS
     * @param $param ['task_data'] String 必传 调用接口时使用POST方式传递给接口的数据（RAW DATA方式）
     * @return bool
     * @throws Exception
     */
    static public function secondLevelSchedulerAdd($param)
    {
        $method = '/services/v3/task/add';
        $url    = env('ITEM.SLS_URL') . $method;

        $data_string  = json_encode($param);
        $url_header   = [];
        $url_header[] = 'vinehoo-client: tp6-auction';
        $url_header[] = 'vinehoo-client-version: v3';
        $url_header[] = 'Content-Length: ' . strlen($data_string);

        $code   = httpCurl($url, 'post', $data_string, 3, $url_header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            $desc = '添加秒级任务';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception("{$desc}错误, " . ($result['error_msg'] ?? ($result['msg'] ?? '')));
        }

        return true;
    }

    /**
     * @方法描述:更新秒级任务时间、接口地址或参数
     * <AUTHOR>
     * @Date 2022/12/19 22:18
     * @param $param
     * @param $param ['old_task_id'] String 必传 被更新的任务ID
     * @param $param ['new_task_id'] String 必传 新的任务ID，请使用GUID方式生成唯一值
     * @param $param ['task_trigger_time'] TimeStamp 必传 任务触发时间，10位时间戳
     * @param $param ['task_url'] String 必传 任务触发时调用的接口地址，必须带上HTTP或HTTPS
     * @param $param ['task_data'] String 必传 调用接口时使用POST方式传递给接口的数据（RAW DATA方式）
     * @return bool
     * @throws Exception
     */
    static public function secondLevelSchedulerUpdate($param)
    {
        $method = '/services/v3/task/update';
        $url    = env('ITEM.SLS_URL') . $method;

        $data_string  = json_encode($param);
        $url_header   = [];
        $url_header[] = 'vinehoo-client: tp6-auction';
        $url_header[] = 'vinehoo-client-version: v3';
        $url_header[] = 'Content-Length: ' . strlen($data_string);

        $code   = httpCurl($url, 'post', $data_string, 3, $url_header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0 || !isset($result['data'])) {
            $desc = '更新任务时间、接口地址或参数';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception("{$desc}错误, " . $result['error_msg'] ?? '');
        }

        return true;
    }

    /**
     * @方法描述: 拍卖信用分更新
     * <AUTHOR>
     * @Date 2022/12/21 11:36
     * @param $param
     * @param $param ['uid'] 必传 int 用户ID
     * @param $param ['operation'] 必传 int 操作：0:增加,1:减少
     * @param $param ['reason'] 必传 int 原因
     * @param $param ['score'] 必传 int 分数
     * @return bool
     * @throws Exception
     */
    static public function creditScoreUpdate($param)
    {
        $method = '/user/v3/auction/CreditScoreUpdate';
        $url    = env('ITEM.USER_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            \think\facade\Log::error("creditScoreUpdate 拍卖信用分更新 : " . json_encode($param) . '; result: ' . $code);
            return $result['error_msg'] ?? ($result['msg'] ?? '更新失败');
        }

        return true;
    }

    /**
     * @方法描述: 身份证实名校验
     * <AUTHOR>
     * @Date 2022/12/21 11:36
     * 文档地址 https://showdoc.wineyun.com//web/#/37/3942
     * @param $param
     * @param $param ['idcard'] 必传 string 身份证号码
     * @param $param ['name'] 必传 string 姓名
     * @return bool
     * @throws Exception
     */
    static public function eidCheck($param)
    {
        $method = '/user/v3/user/eidCheck';
        $url    = env('ITEM.USER_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if ((!$result) || ($result['error_code'] != 0)) {
            \think\facade\Log::write('eidCheck 身份证验证错误: ' . $code);
            throw new Exception(($result['error_msg'] ?? ($result['msg'] ?? '验证失败')));
        }

        if (empty($result['data'])) {
            throw new Exception("身份证号错误!");
        }

        return $result['data'];
    }

    /**
     * @方法描述: 三方订单推送
     * @文档地址: https://showdoc.wineyun.com//web/#/42/2058
     * <AUTHOR>
     * @Date 2023/2/21 15:23
     * @param $param
     * @return array
     */
    static public function tripartiteOrderPush($param): array
    {
        $method = '/orders/v3/tripartite/create';
        $url    = env('ITEM.ORDERS_URL') . $method;

        $code   = httpCurl($url, 'post', json_encode($param), 3);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            $desc = '三方订单推送';
            \think\facade\Log::error(__FUNCTION__ . " {$desc}错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            return [false, $code];
        }

        return [true, $code];
    }

    /**
     * @方法描述: 发送机器人消息
     * <AUTHOR>
     * @Date 2023/2/21 13:37
     * @param $param
     * @return bool
     * @throws Exception
     */
    static public function sendWechatSender($param)
    {
        $header[] = 'vinehoo-client:tp6-orders';
        $url      = env('ITEM.QUEUE_URL');

        $basedata = [
            'access_token' => $param['access_token'] ?? '9dd56154-a216-4f16-886e-e865fe106652',
            'type'         => 'text',
            'content'      => base64_encode($param['msg']),
            'at'           => $param['at'] ?? '', // 中台用户手机号
        ];

        $code   = httpCurl($url, 'post', json_encode([
            'exchange_name' => 'dingtalk',//  dingtalk
            'routing_key'   => 'dingtalk_sender', //  dingtalk_sender
            'data'          => base64_encode(json_encode($basedata)),
        ]), 5, $header);
        $result = json_decode($code, true);

        if (!$result || $result['error_code'] != 0) {
            \think\facade\Log::error(__FUNCTION__ . " 发送机器人消息 加入队列错误: " . json_encode([$url, 'post', $param]) . ' 请求结果: ' . $code);
            throw new Exception('发送机器人消息 加入队列错误');
        }
        return true;

    }


    static public function getCorpbyCode($param)
    {
        $codes       = $param['codes'];
        $codes_group = array_chunk($codes, 100);

        $data = [];
        foreach ($codes_group as $codes) {
            $param['codes'] = $codes;
            $method         = '/erp/v3/purchaseOrder/getCorpbyCode';
            $url            = 'https://callback.vinehoo.com/erp' . $method;

            $code   = httpCurl($url, 'post', json_encode($param), 3);
            $result = json_decode($code, true);

            if ((!$result) || ($result['error_code'] != 0)) {
                \think\facade\Log::write('getCorpbyCode 获取采购订单所属ERP: ' . $code);
                throw new Exception(($result['error_msg'] ?? ($result['msg'] ?? '失败')));
            }

            $data = array_merge($data, $result['data']);
        }

        return $data;
    }

    /**
     * @方法描述: 快递100查询物流轨迹
     * <AUTHOR>
     * @Date 2023/6/26 15:18
     * @param $param
     * @return array|mixed
     */
    static public function kuaidi100MapTrack($param)
    {
        $url    = 'https://callback.vinehoo.com/logistics/logistics/mapTrack/v3/track?' . self::array_to_url_prarm($param);
        $code   = httpCurl($url, 'get', '', 3);
        $result = json_decode($code, true);
        return $result['data'] ?? [];
    }

    static public function receiveOrderSync($param)
    {
        $desc         = '萌芽撤单(退货)';
        $method       = '/sync/shiporder/receive';
        $url          = env('ITEM.OUTBOUND_URL') . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $param['store_code']    = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值
        $param['refund_status'] = $param['refund_status'] ?? 1; // 1=撤单

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    static protected function request($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = $param['desc'] ?? '未定义请求';    //请求描述
        $header       = $param['header'] ?? [];    //请求头
        #region 发起请求
        $request_body = [];
        if (!empty($param['param'])) {
            if ($param['request_type'] == 'get') {
                $param['url'] = $param['url'] . '?' . http_build_query($param['param']);
            } elseif ($param['request_type'] == 'post') {
                $request_body = $param['param'];
            }
        }
        $request_body = json_encode($request_body);
        $code         = httpCurl($param['url'], $param['request_type'], $request_body, 30, $header);

//        $code = '{"error_code":0,"error_msg":"ok","data":{"P757510535177509371":{"u8c_029_amount":"179.00","u8c_515_amount":0,"t_plus_002_amount":0,"t_plus_008_amount":0},"P757516052081375111":{"u8c_029_amount":"165.00","u8c_515_amount":0,"t_plus_002_amount":0,"t_plus_008_amount":0},"P757516738787055251":{"u8c_029_amount":"55.00","u8c_515_amount":0,"t_plus_002_amount":0,"t_plus_008_amount":0},"P757519082564218751":{"u8c_029_amount":"91.00","u8c_515_amount":0,"t_plus_002_amount":0,"t_plus_008_amount":0},"P757561512715317271":{"u8c_029_amount":"91.00","u8c_515_amount":0,"t_plus_002_amount":0,"t_plus_008_amount":0},"P758209817049231261":{"u8c_029_amount":"89.00","u8c_515_amount":0,"t_plus_002_amount":0,"t_plus_008_amount":0}}}';

        $result = json_decode($code, true);
        #endregion 发起请求

        if (!$result || $result[$code_field] != $success_code) {
            \think\facade\Log::write("CURL发起请求错误：url:{$param['url']}, method:{$param['request_type']}，body:{$request_body}, code:{$code}");
            throw new Exception("{$desc}错误: " . ($result[$msg_field] ?? ($code ?? '未知错误')));
        }

        return $result;
    }


    static public function orderPush($param)
    {
        $desc         = '萌芽推单';
        $method       = '/pushorders/v3/order/push';
        $url          = env('ITEM.PUSH_ORDERS_URL') . $method;
        $request_type = "post"; //get post
        $data         = compact('desc', 'param', 'url', 'request_type');

        $result = self::request($data);
        return $result['data'];
    }

    static public function sepcActivityGoodsAdd($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '专题活动添加商品';
        $method       = '/activity/v3/goods/create';
        $url          = env('ITEM.ACTIVITIES_MANAGEMENT_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    //相识度写入
    static public function similarInsert($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '相似度查询新增数据到数据集中';
        $method       = '/services/v3/similar/insert';
        $url          = self::getFullUrlByMethod($method, 'similar');
        $request_type = "post"; //get post

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return true;
    }

    //相识度查询
    static public function similarFind($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '相似度查询新增数据到数据集中';
        $method       = '/services/v3/similar/find';
        $url          = self::getFullUrlByMethod($method, 'similar');
        $request_type = "get"; //get post

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    static public function getFullUrlByMethod($method, $module = null)
    {
        $modules = [
            'similar'               => 'SIMILAR_URL',
            'DEV_CONFIG_CENTER_URL' => 'DEV_CONFIG_CENTER_URL',
            'CONFIG_CENTER_URL'     => 'CONFIG_CENTER_URL',
        ];
        if ($module == null) {
            $method = trim($method, '/');
            $module = explode('/', $method)[0] ?? '';
        }
        $method_url = env('ITEM.' . ($modules[$module] ?? ''));
        if (empty($method_url)) throw new Exception('未定义模块: ' . $module);

        return $method_url . $method;
    }

    static public function embeddings($param)
    {
        $desc           = '转化为向量';
        $param['model'] = 2;
        $url            = 'https://chatgpt.vinehoo.com/emb/embedding/v3/embedding/text';
        $request_type   = "post"; //get post
        $result         = httpCurl($url, $request_type, json_encode($param), 30);
        $result         = json_decode($result, true);
        return $result['data']['embedding'];
    }

    static public function embInsert($param)
    {
        $api_key         = 'sk-kRtyau4DY7vGAjNSIbBS470vE28O1E8D22336D08411EE873746087163A2E0';
        $collection_name = 'addr';
        $host            = 'https://vrs-cn-lbj3o4h7t0001k.dashvector.cn-hangzhou.aliyuncs.com';

        $url    = "{$host}/v1/collections/{$collection_name}/docs";
        $header = [
            "dashvector-auth-token: {$api_key}"
        ];

        $result = httpCurl($url, 'post', json_encode($param), 30, $header);

        return $result;
    }


    static public function embQuery($param)
    {
        $api_key         = 'sk-kRtyau4DY7vGAjNSIbBS470vE28O1E8D22336D08411EE873746087163A2E0';
        $collection_name = 'addr';
        $host            = 'https://vrs-cn-lbj3o4h7t0001k.dashvector.cn-hangzhou.aliyuncs.com';

        $url    = "{$host}/v1/collections/{$collection_name}/query";
        $header = [
            "dashvector-auth-token: {$api_key}"
        ];

        $result = httpCurl($url, 'post', json_encode($param), 30, $header);

        return $result;
    }


    //分词模式。full - 完整模式 search - 搜索引擎模式
    static public function jiebaSegment($param, $model = 'full')
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $desc         = '使用结巴对中文句子进行分词';
        $method       = '/segment/jieba?mode=' . $model;
        $url          = 'https://test-wine.wineyun.com/py3-pdf-generator' . $method;
        $request_type = "post"; //get post

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);

        unset($result['error_code'], $result['error_msg']);
        return $result;
    }

    static public function addressAiMatch($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $timeout      = $param['timeout'] ?? 30;    //超时时间
        $desc         = '收货地址智能匹配';
        $method       = '/user/v3/address/AiMatch';
        $url          = env('ITEM.USER_URL') . $method;
        $request_type = "get"; //get post

        $data   = compact('timeout', 'code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        if (empty($result['data'])) throw new Exception('地址解析失败!');
        return $result['data'];
    }

    static public function exhibProductsSync($param)
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '同步售卖状态';
        $method       = '/marketing-conf/v3/exhib/products/sync';
        $url          = env('ITEM.MARKET_CONF_URL') . $method;
        $request_type = "post"; //get post

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type');
        $result = self::request($data);
        return $result['data'];
    }

    static public function goodsGetFictitiousCount($param)
    {
        $desc         = '根据商品条码/简码获取虚拟仓库库存';
        $method       = '/query/goodsGetFictitiousCount';
        $url          = env('ITEM.DISTRIBUTE_URL') . $method; //outbound
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    static public function getTrilateralOrderStatus($param)
    {
        $desc         = '获取三方订单状态';
        $method       = '/orders_server/v3/trilateral/get_trilateral_order_status';
        $url          = env('ITEM.ORDERS_MICRO_SERVICE_URL') . $method; //outbound
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    static public function queueProdPush($param)
    {

        $desc         = '推送到生产队列';
        $url          = env('ITEM_PROD.QUEUE_URL');
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $header[]     = 'vinehoo-client:manual';

        $data = compact('header', 'desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return true;
    }

    static public function receiptInfo($param)
    {
        $desc   = '同步修改发货单信息';
        $method = '/sync/shiporder/receiptInfo';
        $url    = env('ITEM.DISTRIBUTE_URL') . $method; //outbound
        $url    = "https://wms.vinehoo.com/outbound/sync/shiporder/receiptInfo"; //outbound 不限制IP

        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'errorCode'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $param['store_code'] = $param['store_code'] ?? 'xGpS0mPbNqo5FMQcdTk4nR1DJr9LUEfa';//固定值

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    static public function whitelistSet($param, $module = 'DEV_CONFIG_CENTER_URL')
    {
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码
        $desc         = '更新网关接口白名单';
        $method       = '/config/v3/apis/whitelist/set';
        $url          = self::getFullUrlByMethod($method, $module);

        $request_type = "post"; //get post
        $header       = [
            'vinehoo-client:批量操作',
            'vinehoo-client-version:1',
            'Content-Type:application/json'
        ];

        $data   = compact('code_field', 'msg_field', 'success_code', 'desc', 'param', 'url', 'request_type', 'header');
        $result = self::request($data);
        return true;
    }

    //https://showdoc.wineyun.com/web/#/94/5448
    static public function skCreate($param)
    {
        $desc         = '创建收款单';
        $method       = '/erp/v3/arap/sk/create';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result;
    }

    static public function getXiaoHongShuShopInfo($param)
    {
        $desc         = '获取小红书店铺信息';
        $method       = '/xiaohongshu/v3/shopInfo';
        $url          = env('ITEM.XIAOHONGSHU_SYNC_URL') . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    static public function erpGetAmounts($param)
    {
        $desc         = '获取销售单金额';
        $method       = '/erp/v3/saleOrder/getAmounts';
        $url          = env('ITEM.ERP_URL') . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }

    static public function shortUrlRedirect($param)
    {
        $desc         = '获取跳转最终地址';
        $method       = '/mulandoGreateDestiny/v1/short_url_redirect';
        $url          = 'https://test-wine.wineyun.com/go-mulando-greate-destiny' . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }


    static public function mldProdRsaDec($param)
    {
        $desc         = '正式RSA解密木兰朵';
        $method       = '/mulandoGreateDestiny/v1/decrypt/rsa';
        $url          = 'https://wxapp.vinehoo.com' . $method;
        $request_type = "post"; //get post
        $code_field   = $param['code_field'] ?? 'error_code'; //状态码字段
        $msg_field    = $param['msg_field'] ?? 'error_msg'; //状态描述字段
        $success_code = $param['success_code'] ?? 0;    //成功状态码

        $data = compact('desc', 'param', 'url', 'request_type', 'code_field', 'msg_field', 'success_code');

        $result = self::request($data);
        return $result['data'];
    }


}