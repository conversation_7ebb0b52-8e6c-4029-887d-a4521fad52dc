<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Md;
use app\validate\MdValidate;
use think\facade\Db;
use League\HTMLToMarkdown\HtmlConverter;

/**
 * 埋点
 * Class MdService
 * @package app\service\v3
 */
class MdService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Md;
        $this->validate    = MdValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/11/01 16:52
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion
            
        };
    }


    public function exportUserPhone($param)
    {
        $uids = Db::connect('pord_mysql_maidian')->name('report_default')
            ->where('is_login', 1)
            ->whereTime('created_time', '>=', '2023-05-01 00:00:00')
//            ->limit(0, 10)
            ->group('uid')
            ->column('uid');

        $uids = array_unique($uids);

        $uid_group = array_chunk($uids, 1000);
        $uid_group_count = count($uid_group);

        $data = $enc_err_data = [];

        $i = 1;
        foreach ($uid_group as $uid_items) {
            $enc_datum = Db::connect('pord_mysql_user')->name('user')
                ->where('uid', 'in', $uid_items)
                ->column('telephone');

            $dec_data = \Curl::cryptionDeal($enc_datum);
            if (empty($dec_data)) {
                $enc_err_data[] = $enc_datum;
                $item_res       = 'err';
            } else {
                $data     = array_merge($data, array_values($dec_data));
                $item_res = 'success';
            }

            print_r("{$i}/{$uid_group_count} {$item_res}" . PHP_EOL);
            $i++;
        }

        return $this->exportExcel([
            'filename'   => '5月1日至今登录过用户手机号.xlsx',
            'sheet_name' => '5月1日至今登录过用户手机号',
            'header'     => ['手机号'],
            'data'       => array_values(array_unique($data)),
        ]);

    }

    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            if(is_array($ret_data)){
                $export_data[] = array_values($ret_data);
            }else{
                $export_data[] = [$ret_data];
            }
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }



    /**
     * @方法描述: 将富文本HTML转换为Markdown格式 (使用league/html-to-markdown库)
     * <AUTHOR>
     * @Date 2024/12/19 10:00
     * @param string $html 富文本HTML内容
     * @param array $options 转换选项配置
     * @return string 转换后的Markdown内容
     */
    public function convertHtmlToMarkdown($param)
    {
        $html = $param['html'];
        $options = [];
        if (empty($html)) {
            return '';
        }

        try {
            // 默认配置
            $defaultOptions = [
                'header_style' => 'atx',        // 使用 # 风格的标题
                'bold_style' => '**',           // 粗体使用 **
                'italic_style' => '*',          // 斜体使用 *
                'list_item_style' => '-',       // 无序列表使用 -
                'preserve_comments' => false,   // 不保留HTML注释
                'strip_tags' => false,          // 不删除不支持的标签
                'remove_nodes' => 'script style', // 移除script和style标签
            ];

            // 合并用户配置
            $config = array_merge($defaultOptions, $options);

            // 创建转换器实例
            $converter = new HtmlConverter($config);

            // 执行转换
            $markdown = $converter->convert($html);

            // 清理多余的空行
            $markdown = preg_replace('/\n{3,}/', "\n\n", $markdown);

            print_r(trim($markdown));die;

        } catch (\Exception $e) {
            // 如果转换失败，记录错误并返回原始HTML（去除标签）
            error_log('HTML to Markdown conversion failed: ' . $e->getMessage());
            return strip_tags($html);
        }
    }

    /**
     * @方法描述: 高级HTML转Markdown转换（支持自定义转换器）
     * <AUTHOR>
     * @Date 2024/12/19 10:00
     * @param string $html 富文本HTML内容
     * @param array $customConverters 自定义转换器配置
     * @return string 转换后的Markdown内容
     */
    public function convertHtmlToMarkdownAdvanced($html, $customConverters = [])
    {
        if (empty($html)) {
            return '';
        }

        try {
            $converter = new HtmlConverter([
                'header_style' => 'atx',
                'bold_style' => '**',
                'italic_style' => '*',
                'list_item_style' => '-',
                'preserve_comments' => false,
                'strip_tags' => false,
                'remove_nodes' => 'script style',
            ]);

            // 添加自定义转换器
            foreach ($customConverters as $tag => $converterClass) {
                if (class_exists($converterClass)) {
                    $converter->getConfig()->setOption('custom_converters', [
                        $tag => new $converterClass()
                    ]);
                }
            }

            $markdown = $converter->convert($html);
            return trim(preg_replace('/\n{3,}/', "\n\n", $markdown));

        } catch (\Exception $e) {
            error_log('Advanced HTML to Markdown conversion failed: ' . $e->getMessage());
            return strip_tags($html);
        }
    }



}



