<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Md;
use app\validate\MdValidate;
use think\facade\Db;

/**
 * 埋点
 * Class MdService
 * @package app\service\v3
 */
class MdService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Md;
        $this->validate    = MdValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/11/01 16:52
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {
            
            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion
            
        };
    }


    public function exportUserPhone($param)
    {
        $uids = Db::connect('pord_mysql_maidian')->name('report_default')
            ->where('is_login', 1)
            ->whereTime('created_time', '>=', '2023-05-01 00:00:00')
//            ->limit(0, 10)
            ->group('uid')
            ->column('uid');

        $uids = array_unique($uids);

        $uid_group = array_chunk($uids, 1000);
        $uid_group_count = count($uid_group);

        $data = $enc_err_data = [];

        $i = 1;
        foreach ($uid_group as $uid_items) {
            $enc_datum = Db::connect('pord_mysql_user')->name('user')
                ->where('uid', 'in', $uid_items)
                ->column('telephone');

            $dec_data = \Curl::cryptionDeal($enc_datum);
            if (empty($dec_data)) {
                $enc_err_data[] = $enc_datum;
                $item_res       = 'err';
            } else {
                $data     = array_merge($data, array_values($dec_data));
                $item_res = 'success';
            }

            print_r("{$i}/{$uid_group_count} {$item_res}" . PHP_EOL);
            $i++;
        }

        return $this->exportExcel([
            'filename'   => '5月1日至今登录过用户手机号.xlsx',
            'sheet_name' => '5月1日至今登录过用户手机号',
            'header'     => ['手机号'],
            'data'       => array_values(array_unique($data)),
        ]);

    }

    public function exportExcel($param)
    {
        $filename   = $param['filename'] ?? '导出数据数据.xls';
        $sheet_name = $param['sheet_name'] ?? 'sheet1';
        $header     = $param['header'] ?? [];
        $data       = $param['data'] ?? [];

        $path = app()->getRuntimePath() . 'excel';
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $excel = new \Vtiful\Kernel\Excel(compact('path'));
        $excel->fileName($filename, $sheet_name);

        $export_data = [];
        foreach ($data as $ret_data) {
            if(is_array($ret_data)){
                $export_data[] = array_values($ret_data);
            }else{
                $export_data[] = [$ret_data];
            }
        }

        $filePath = $excel
            ->header($header)
            ->data($export_data)
            ->output();

        return $filePath;
    }



    /**
     * @方法描述: 将富文本HTML转换为Markdown格式
     * <AUTHOR>
     * @Date 2024/12/19 10:00
     * @param string $html 富文本HTML内容
     * @return string 转换后的Markdown内容
     */
    public function convertHtmlToMarkdown($html)
    {
        if (empty($html)) {
            return '';
        }

        // 预处理：移除多余的空白字符
        $html = preg_replace('/\s+/', ' ', $html);
        $html = trim($html);

        // 转换规则映射
        $conversions = [
            // 标题转换 h1-h6
            '/<h1[^>]*>(.*?)<\/h1>/i' => '# $1',
            '/<h2[^>]*>(.*?)<\/h2>/i' => '## $1',
            '/<h3[^>]*>(.*?)<\/h3>/i' => '### $1',
            '/<h4[^>]*>(.*?)<\/h4>/i' => '#### $1',
            '/<h5[^>]*>(.*?)<\/h5>/i' => '##### $1',
            '/<h6[^>]*>(.*?)<\/h6>/i' => '###### $1',

            // 粗体和斜体
            '/<strong[^>]*>(.*?)<\/strong>/i' => '**$1**',
            '/<b[^>]*>(.*?)<\/b>/i' => '**$1**',
            '/<em[^>]*>(.*?)<\/em>/i' => '*$1*',
            '/<i[^>]*>(.*?)<\/i>/i' => '*$1*',

            // 删除线
            '/<del[^>]*>(.*?)<\/del>/i' => '~~$1~~',
            '/<s[^>]*>(.*?)<\/s>/i' => '~~$1~~',
            '/<strike[^>]*>(.*?)<\/strike>/i' => '~~$1~~',

            // 代码
            '/<code[^>]*>(.*?)<\/code>/i' => '`$1`',

            // 链接
            '/<a[^>]*href=["\']([^"\']*)["\'][^>]*>(.*?)<\/a>/i' => '[$2]($1)',

            // 图片
            '/<img[^>]*src=["\']([^"\']*)["\'][^>]*alt=["\']([^"\']*)["\'][^>]*\/?>/i' => '![$2]($1)',
            '/<img[^>]*alt=["\']([^"\']*)["\'][^>]*src=["\']([^"\']*)["\'][^>]*\/?>/i' => '![$1]($2)',
            '/<img[^>]*src=["\']([^"\']*)["\'][^>]*\/?>/i' => '![]($1)',

            // 换行
            '/<br[^>]*\/?>/i' => "\n",

            // 段落
            '/<p[^>]*>(.*?)<\/p>/i' => "$1\n\n",

            // 水平线
            '/<hr[^>]*\/?>/i' => "\n---\n",
        ];

        // 执行基本转换
        foreach ($conversions as $pattern => $replacement) {
            $html = preg_replace($pattern, $replacement, $html);
        }

        // 处理列表
        $html = $this->convertLists($html);

        // 处理引用
        $html = $this->convertBlockquotes($html);

        // 处理代码块
        $html = $this->convertCodeBlocks($html);

        // 处理表格
        $html = $this->convertTables($html);

        // 清理HTML标签
        $html = strip_tags($html);

        // 解码HTML实体
        $html = html_entity_decode($html, ENT_QUOTES, 'UTF-8');

        // 清理多余的空行
        $html = preg_replace('/\n{3,}/', "\n\n", $html);

        return trim($html);
    }

    /**
     * @方法描述: 转换列表
     * @param string $html
     * @return string
     */
    private function convertLists($html)
    {
        // 处理无序列表
        $html = preg_replace_callback('/<ul[^>]*>(.*?)<\/ul>/is', function($matches) {
            $content = $matches[1];
            $content = preg_replace('/<li[^>]*>(.*?)<\/li>/is', '- $1', $content);
            return "\n" . trim($content) . "\n";
        }, $html);

        // 处理有序列表
        $html = preg_replace_callback('/<ol[^>]*>(.*?)<\/ol>/is', function($matches) {
            $content = $matches[1];
            $counter = 1;
            $content = preg_replace_callback('/<li[^>]*>(.*?)<\/li>/is', function($li_matches) use (&$counter) {
                return $counter++ . '. ' . trim($li_matches[1]);
            }, $content);
            return "\n" . trim($content) . "\n";
        }, $html);

        return $html;
    }

    /**
     * @方法描述: 转换引用块
     * @param string $html
     * @return string
     */
    private function convertBlockquotes($html)
    {
        return preg_replace_callback('/<blockquote[^>]*>(.*?)<\/blockquote>/is', function($matches) {
            $content = trim($matches[1]);
            $lines = explode("\n", $content);
            $quotedLines = array_map(function($line) {
                return '> ' . trim($line);
            }, $lines);
            return "\n" . implode("\n", $quotedLines) . "\n";
        }, $html);
    }

    /**
     * @方法描述: 转换代码块
     * @param string $html
     * @return string
     */
    private function convertCodeBlocks($html)
    {
        return preg_replace_callback('/<pre[^>]*><code[^>]*>(.*?)<\/code><\/pre>/is', function($matches) {
            $content = trim($matches[1]);
            return "\n```\n" . $content . "\n```\n";
        }, $html);
    }

    /**
     * @方法描述: 转换表格
     * @param string $html
     * @return string
     */
    private function convertTables($html)
    {
        return preg_replace_callback('/<table[^>]*>(.*?)<\/table>/is', function($matches) {
            $tableContent = $matches[1];
            $markdown = "\n";

            // 处理表头
            if (preg_match('/<thead[^>]*>(.*?)<\/thead>/is', $tableContent, $theadMatches)) {
                $theadContent = $theadMatches[1];
                if (preg_match('/<tr[^>]*>(.*?)<\/tr>/is', $theadContent, $trMatches)) {
                    $headers = [];
                    preg_match_all('/<th[^>]*>(.*?)<\/th>/is', $trMatches[1], $thMatches);
                    foreach ($thMatches[1] as $header) {
                        $headers[] = trim(strip_tags($header));
                    }
                    if (!empty($headers)) {
                        $markdown .= '| ' . implode(' | ', $headers) . ' |' . "\n";
                        $markdown .= '| ' . str_repeat('--- | ', count($headers)) . "\n";
                    }
                }
            }

            // 处理表体
            if (preg_match('/<tbody[^>]*>(.*?)<\/tbody>/is', $tableContent, $tbodyMatches)) {
                $tbodyContent = $tbodyMatches[1];
            } else {
                $tbodyContent = $tableContent;
            }

            preg_match_all('/<tr[^>]*>(.*?)<\/tr>/is', $tbodyContent, $trMatches);
            foreach ($trMatches[1] as $row) {
                $cells = [];
                preg_match_all('/<td[^>]*>(.*?)<\/td>/is', $row, $tdMatches);
                foreach ($tdMatches[1] as $cell) {
                    $cells[] = trim(strip_tags($cell));
                }
                if (!empty($cells)) {
                    $markdown .= '| ' . implode(' | ', $cells) . ' |' . "\n";
                }
            }

            return $markdown . "\n";
        }, $html);
    }

}



