<?php
declare (strict_types=1);

namespace app\command;

use app\service\v3\OrdersService;
use app\service\v3\ScriptService;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class WmsDataSeparation extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('wmsDataSeparation')
            ->setDescription('the wmsDataSeparation command');
    }

    protected function execute(Input $input, Output $output)
    {
//        $res = (new WmsService())->dataSeparation(['show' => false]);
//        $res = (new CommoditiesService())->syncSecondData([]); //
//        $res = (new CommoditiesService())->syncFlashData([]); //
//        $res = (new CommoditiesService())->syncFlashDataProdBath([]); //导出闪购同步商品
//        $res = (new CommoditiesService())->periodsSecondMerchantsSync(); //商家秒发销量同步
//        $res = (new CommoditiesService())->flashPeriodsExport([]); // 导出闪购非渠道商品
//        $res = (new Orders())->purchasedUser([]); //导出 4月1日至今，在酒云网购买过商品的用户 手机号
//        $res = (new Orders())->jxUser202311([]); //售前客服绩效202311数据导出
//        $res = (new OrdersService())->clearTable(); //清理数据
//        $res = (new OrdersService())->sales2023(); //2023年销售数据
//        $res = (new OrdersService())->sytjsj(); //2023年销售数据
//        $res = (new OrdersService())->orderslist2023(); //有支付时间且下单时间在23年的所有酒云网订单
//        $res = (new OrdersService())->ptjsales2023(); //葡萄酒期数销售数据详情
//        $res = (new OrdersService())->bj20230912(); //23年白酒9至12月的订单明细
//        $res = (new OrdersService())->crossAutoPush(); //跨境自动推单
//        $res = (new OrdersService())->crossPeriodChangeByExcel([]); //跨境期数根据Excel更新
//        $res = (new OrdersService())->smzdmLrl(); //什么值得买利润率
//        $res = (new OrdersService())->smzdmOrderStatus(); //什么值得买订单状态
//        $res = (new OrdersService())->checkNetwork(); //网络状态检测
//        $res = (new OrdersService())->allOrdersPhone(); //购买记录用户手机号导出
//        $res = (new OrdersService())->crossMonitoring(); //跨境违规监控
//        $res = (new OrdersService())->crossAddressList(); //跨境收货地址导出
//        $res = (new OrdersService())->pushAddressToAli(); //跨境收货地址导出
//        $res = (new OrdersService())->crossExport(); //跨境收货地址导出
//        $res = (new OrdersService())->crossExport(); //跨境收货地址导入到相似度查询
//        $res = (new OrdersService())->periodNewUsers([]); //跨境收货地址导入到相似度查询
//        $res = (new OrdersService())->qj202201PeriodsExport([]); //跨境收货地址导入到相似度查询
//        $res = (new OrdersService())->supplychainData([]); //跨境收货地址导入到相似度查询
//        $res = (new OrdersService())->supplychainAddress([]); //跨境收货地址导入到相似度查询
//        $res = (new MdService())->exportUserPhone([]); //导出 5月1日至今登录用户手机号
//        $res = (new AuctionService())->wmsCancelOrder(); // 正式拍卖萌芽撤单
//        $res = (new OrdersService())->liquorSoldNum(); // 23年酒类产品销量
//        $res = (new OrdersService())->batchCancellation([]); // 批量取消订单
//        $res = (new OrdersService())->syncCustomerPriceBook([]); //同步客户价格本
//        $res = (new OrdersService())->bulkCouponByPeriod([]); //同步客户价格本
//        $res = (new OrdersService())->exhibProductSync([]); //同步客户价格本
//        $res = (new OrdersService())->appendPeriodsFields([]); //追加期数字段
//        $res = (new OrdersService())->consumptionGen1000usersPhone([]); //追加期数字段
//        $res = (new OrdersService())->supplychainConnect([]); //追加期数字段
//        $res = (new OrdersService())->purchaseList([]); //供应商交接数据导出
//        $res = (new OrdersService())->exportOfflineOrders([]); //供应商交接数据导出
//        $res = (new OrdersService())->crossPeriodsUpdate([]); //跨境期数更新
//        $res = (new OrdersService())->inventoryProductExport([]); //仓库内EVA采购的库存产品
//        $res = (new OrdersService())->whitelist(); //白名单
//        $res = (new OrdersService())->whitelistTP(); //白名单
//        $res = (new OrdersService())->mldckOrders([]); //线下木兰朵仓库订单
//        $res = (new OrdersService())->jmzxOrders([]); //简码对应最新订单
//        $res = (new OrdersService())->ermsjzh([]); //简码对应最新订单
//        $res = (new OrdersService())->jmzxOrdersByExcel([]); //简码对应最新订单 空白补缺
//        $res = (new OrdersService())->q1xltj([]); //闪购秒发Q1销量统计
//        $res = (new OrdersService())->partnerEntityBmSync([]); //往来单位部门业务员数据同步
//        $res = (new OrdersService())->sfdddr([]); //三方订单导入
//        $res = (new OrdersService())->sfddfh([]); //更新三方订单发货状态,发货单号
//        $res = (new OrdersService())->changeCrossOrdersByExcel([]); //批量修改简码
//        $res = (new OrdersService())->allPeriodsExport([]); //导出所有期数
//        $res = (new OrdersService())->periodExport([]); //期数导出
//        $res = (new OrdersService())->userExport([]); //用户数据导出
//        $res = (new OrdersService())->changeTrOrders([]); //用户数据导出
//        $res = (new OrdersService())->snxssj([]); //广州深圳销售数据统计
//        $res = (new OrdersService())->zgptj([]); //中国专区葡萄酒销售情况
//        $res = (new OrdersService())->offlineOrdersUpdate([]); //线下订单更新
//        $res = (new OrdersService())->yhysjdddc([]); //一花一世界订单导出
//        $res = (new OrdersService())->batchOrderUpdate([]); //批量更新现付金额 - 余额上线
//        $res = (new OrdersService())->customsDirectPush([]); //跨境直接推单
//        $res = (new OrdersService())->channelOrdersExport([]); //跨境直接推单
//        $res = (new OrdersService())->userAmountExport(); //跨境直接推单
//        $res = (new OrdersService())->appendUserBuys(); //跨境直接推单

        $res = (new ScriptService())->getTrOrders([]); //获取三方订单
//        $res = (new ScriptService())->receivedAmountAllocation([]); //获取三方订单
        // 指令输出
//        $output->writeln('wmsDataSeparation: '.$res);
//        $output->writeln('syncSecondData: '.$res);
    }
}

// php think wmsDataSeparation